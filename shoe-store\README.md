# SoleStyle - Premium Shoe Store

Una página web profesional de venta de zapatos construida con React, GSAP, Lenis y shadcn/ui.

## 🚀 Características

- **Diseño Moderno**: Interfaz elegante y profesional
- **Animaciones Fluidas**: Implementadas con GSAP para micro-interacciones y transiciones
- **Smooth Scrolling**: Experiencia de navegación suave con Lenis
- **Responsive Design**: Optimizado para todos los dispositivos
- **Componentes Reutilizables**: Arquitectura modular con shadcn/ui
- **Filtros Avanzados**: Búsqueda y filtrado por categoría, marca, precio
- **Carrito de Compras**: Funcionalidad completa de e-commerce

## 🛠️ Tecnologías Utilizadas

- **React 18+** - Framework de JavaScript
- **Vite** - Build tool y dev server
- **GSAP** - Librería de animaciones
- **Lenis** - Smooth scrolling
- **Tailwind CSS** - Framework de CSS
- **shadcn/ui** - Componentes de UI
- **Lucide React** - Iconos

## 📦 Instalación

1. Clona el repositorio:
```bash
git clone [url-del-repositorio]
cd shoe-store
```

2. Instala las dependencias:
```bash
npm install
```

3. Inicia el servidor de desarrollo:
```bash
npm run dev
```

4. Abre tu navegador en `http://localhost:5174`

## 🏗️ Estructura del Proyecto

```
src/
├── components/          # Componentes React
│   ├── ui/             # Componentes de shadcn/ui
│   ├── Header.jsx      # Navegación principal
│   ├── Hero.jsx        # Sección hero
│   ├── ProductCard.jsx # Tarjeta de producto
│   ├── ProductGrid.jsx # Grid de productos
│   ├── FeaturedProducts.jsx # Productos destacados
│   ├── Stats.jsx       # Estadísticas
│   └── Footer.jsx      # Pie de página
├── hooks/              # Custom hooks
│   └── useLenis.js     # Hook para Lenis
├── data/               # Datos de ejemplo
│   └── shoes.js        # Productos de zapatos
├── lib/                # Utilidades
│   └── utils.js        # Funciones helper
└── App.jsx             # Componente principal
```

## 🎨 Características de Diseño

### Animaciones GSAP
- Animaciones de entrada para secciones
- Hover effects en productos
- Transiciones suaves entre estados
- Parallax effects
- Contador animado de estadísticas

### Smooth Scrolling con Lenis
- Navegación fluida
- Integración con GSAP ScrollTrigger
- Optimizado para rendimiento

### Componentes Interactivos
- Carrusel de imágenes de productos
- Filtros dinámicos
- Búsqueda en tiempo real
- Selector de tallas y colores
- Carrito de compras animado

## 📱 Responsive Design

- **Mobile First**: Diseño optimizado para móviles
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Navegación Móvil**: Menú hamburguesa con animaciones
- **Grid Adaptativo**: Layout que se ajusta según el dispositivo

## 🛒 Funcionalidades de E-commerce

- Catálogo de productos con filtros
- Búsqueda por nombre, marca o categoría
- Filtros por precio, marca y categoría
- Selector de tallas y colores
- Carrito de compras
- Productos destacados
- Indicadores de stock

## 🎯 Optimizaciones

- **Performance**: Lazy loading de imágenes
- **SEO**: Estructura semántica HTML
- **Accesibilidad**: Navegación por teclado y screen readers
- **Bundle Size**: Code splitting y tree shaking

## 🚀 Scripts Disponibles

- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run preview` - Previsualiza la build de producción

## 📄 Licencia

Este proyecto está bajo la Licencia MIT.