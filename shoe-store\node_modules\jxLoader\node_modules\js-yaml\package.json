{"name": "js-yaml", "version": "0.3.7", "description": "YAML 1.1 Parser", "keywords": ["yaml", "parser", "pyyaml"], "homepage": "https://github.com/nodeca/js-yaml", "author": "Aleksey V <PERSON> <<EMAIL>> (http://www.ixti.net/)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>>", "url": "http://got-ravings.blogspot.com"}], "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "license": {"type": "MIT", "url": "https://github.com/nodeca/js-yaml/blob/master/LICENSE"}, "repository": {"type": "git", "url": "git://github.com/nodeca/js-yaml.git"}, "main": "./index.js", "scripts": {"test": "make test"}, "devDependencies": {"vows": "~ 0.6.0", "jslint": "https://github.com/reid/node-jslint/tarball/6131ebf5713274871b89735105e3286131804771"}, "engines": {"node": "> 0.4.11"}}