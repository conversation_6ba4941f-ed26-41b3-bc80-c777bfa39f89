{"name": "shoe-store", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "build": "^0.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lenis": "^1.3.3", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}}