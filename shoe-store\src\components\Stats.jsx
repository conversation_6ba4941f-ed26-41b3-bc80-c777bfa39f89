import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Users, Package, Award, Truck } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const Stats = () => {
  const sectionRef = useRef(null);

  const stats = [
    {
      icon: Users,
      number: 50000,
      suffix: '+',
      label: 'Happy Customers',
      description: 'Satisfied customers worldwide'
    },
    {
      icon: Package,
      number: 1000,
      suffix: '+',
      label: 'Premium Products',
      description: 'Curated collection of shoes'
    },
    {
      icon: Award,
      number: 25,
      suffix: '+',
      label: 'Top Brands',
      description: 'Leading footwear brands'
    },
    {
      icon: Truck,
      number: 99,
      suffix: '%',
      label: 'On-Time Delivery',
      description: 'Fast and reliable shipping'
    }
  ];

  useEffect(() => {
    // Animate numbers counting up
    stats.forEach((stat, index) => {
      const element = document.querySelector(`.stat-number-${index}`);
      if (element) {
        gsap.fromTo(element,
          { textContent: 0 },
          {
            textContent: stat.number,
            duration: 2,
            ease: 'power2.out',
            snap: { textContent: 1 },
            scrollTrigger: {
              trigger: element,
              start: 'top 80%',
              end: 'bottom 20%',
              toggleActions: 'play none none reverse'
            }
          }
        );
      }
    });

    // Animate stat cards
    gsap.fromTo('.stat-card',
      { opacity: 0, y: 50, scale: 0.9 },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: sectionRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    );
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <div key={index} className="stat-card text-center">
                <div className="bg-white/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="h-8 w-8 text-white" />
                </div>
                <div className="text-4xl font-bold mb-2">
                  <span className={`stat-number-${index}`}>0</span>
                  <span>{stat.suffix}</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">{stat.label}</h3>
                <p className="text-gray-400">{stat.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Stats;